import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import {
  Package,
  Users,
  FileUp,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Database,
  Boxes,
  ChartLine,
  Box,
  ChartNoAxesCombined,
  ShieldCheck,
  Info,
} from "lucide-react";
import InteractiveForecastChart from "@/components/forecasting/InteractiveForecastChart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useGetDashboardData } from "@/hooks/dashboard/useDashboardData";
import { useEffect, useState } from "react";
import { useGetForcastList } from "@/hooks/forcastList/useForcastListHooks";
import { useGetForecastResults } from "@/hooks/generateForcast/generateForcastHooks";
import GlobalLoader from "@/components/common/GlobalLoader";

interface ChartDataPoint {
  date: string;
  actual?: number;
  value: number;
  confidence_lower?: number;
  confidence_upper?: number;
}

export default function DashboardPage() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [forecastResultData, setForecastResultData] = useState([]);
  const [historicalData, setHistoricalData] = useState([]);
  const [actualsData, setActualsData] = useState([]);

  const mockHistoricalData: ChartDataPoint[] = [];

  const { data, isLoading, refetch } = useGetDashboardData();

  const formattedHistoricalData = mockHistoricalData?.map((d) => ({
    date: d.date,
    value: d.value,
  }));

  const formattedForecastData = forecastResultData?.map((d) => ({
    date: d.forecast_date,
    value: d.forecast_quantity,
    confidence_lower: d.forecast_lower,
    confidence_upper: d.forecast_upper,
  }));

  const { checkResult } = useGetForecastResults(
    (data) => {
      console.log(data);
      const resultArrayForecast = data.result.forecast_data || [];
      const resultArrayHistorical = data.result.historical_data || [];
      const resultArrayActuals = data.result.actuals_data || [];
      setForecastResultData(resultArrayForecast);
      setHistoricalData(resultArrayHistorical);
      setActualsData(resultArrayActuals);
    },
    (errorInfo) => {
      // error("Error", `${errorInfo}`);
    }
  );

  useEffect(() => {
    if (dashboardData) {
      checkResult(dashboardData?.last_forecast_progress_id);
    }
  }, [dashboardData]);

  const kpiData = [
    {
      title: "Total Batches",
      value: dashboardData ? dashboardData?.summary_metrics?.total_batches : 0,
      icon: Box,
      iconColor: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Total Forecast",
      value: dashboardData
        ? dashboardData?.summary_metrics?.total_forecasts
        : 0,
      icon: ChartLine,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Total SKUs Forecasted",
      value: dashboardData
        ? dashboardData?.summary_metrics?.total_skus_forecasted
        : 0,
      icon: ChartNoAxesCombined,
      iconColor: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Average Forecast Horizon",
      value: dashboardData
        ? dashboardData?.summary_metrics?.average_forecast_horizon_days
        : 0,
      icon: ShieldCheck,
      iconColor: "text-orange-600",
      bgColor: "bg-orange-50",
    },
  ];

  useEffect(() => {
    if (data && data?.result) {
      setDashboardData(data?.result?.data);
    }
  }, [data]);

  if (isLoading) return <GlobalLoader />

  return (
    <>
      <div className="space-y-6">

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {kpiData.map((kpi) => (
            <div key={kpi.title} className="flashana-card">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-3xl font-bold text-gray-900 mb-1">
                    {kpi.value}
                  </h3>
                  <p className="text-sm font-regular text-gray-700">
                    {kpi.title}
                  </p>
                </div>
                <div className={`p-3 rounded-lg ${kpi.bgColor}`}>
                  <kpi.icon className={`h-6 w-6 ${kpi.iconColor}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Chart container */}
        <Card>
          <CardHeader>
            <CardTitle>Forecast Results</CardTitle>
          </CardHeader>
          <CardContent>
            {
              historicalData.length > 0 && formattedForecastData.length > 0
                ? (
                  <>
                    {/* Info Label */}
                    <div className="pb-4 rounded text-cyan-700 text-sm font-medium flex items-center justify-between">
                      <span className="flex gap-2">
                        <Info size={20} className="min-w-[1rem]" />{" "}
                        <span>
                          The more historical data available, the higher the chances of achieving better forecast accuracy. Limited historical data may result in lower accuracy.
                        </span>
                      </span>
                    </div>
                    <InteractiveForecastChart
                      historicalData={historicalData}
                      forecastData={formattedForecastData}
                      isLoading={formattedForecastData ? false : true}
                      actualsData={actualsData?.length > 0 ? actualsData : []}
                    />
                  </>
                ) : (
                  <div className="text-center py-8 text-gray-500">No forecast data available</div>
                )
            }

          </CardContent>
        </Card>

        {/* Forecast of Top SKU's */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">
              Forecast of Recent SKU's
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Product/SKU</TableHead>
                  <TableHead>Store</TableHead>
                  <TableHead className="text-center">Actual Sales</TableHead>
                  <TableHead className="text-right">Forecast (y)</TableHead>
                  <TableHead className="text-right">y-lower</TableHead>
                  <TableHead className="text-right">y-upper</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forecastResultData.length > 0 ? (
                  forecastResultData.map((row, index) => (
                    <TableRow key={index} className="hover:bg-muted/50">
                      <TableCell className="whitespace-nowrap">
                        {row.forecast_date}
                      </TableCell>
                      <TableCell>{row.sku_code}</TableCell>
                      <TableCell>{row.location_name}</TableCell>
                      <TableCell className="text-center">
                        {row.actual ?? "-"}
                      </TableCell>
                      <TableCell className="text-right">
                        {row.forecast_quantity}
                      </TableCell>
                      <TableCell className="text-right">
                        {row.forecast_lower}
                      </TableCell>
                      <TableCell className="text-right">
                        {row.forecast_upper}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center text-gray-500 py-6">
                      No forecast data available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div >
    </>
  );
}
