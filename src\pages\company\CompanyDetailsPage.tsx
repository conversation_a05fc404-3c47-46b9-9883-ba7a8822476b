import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Pencil, Save } from "lucide-react";
import { Country, State, City } from "country-state-city";
import {
  useGetCompanyDetails,
  useUpdateCompanyDetails,
  useGetCompanyTypes,
} from "../../hooks/CompanyDetails/useCompanyDetails";
import { getUserTenantId } from "@/utils/userInfo";

import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import { useLocation } from "react-router-dom";


// SCHEMA
const schema = yup.object({
  companyName: yup
    .string()
    .required("Company name is required")
    .test("no-leading-space", "Company name cannot start with a space", (v) => !/^\s/.test(v || "")),
  companyType: yup.string().required("Company type is required"),
  numberOfStores: yup.number(),
  websiteURL: yup
    .string()
    .url("Invalid website URL")
    .test("no-leading-space", "Website URL cannot start with a space", (v) => !/^\s/.test(v || "")),
  country: yup.string().required("Country is required"),
  state: yup.string().required("State / Region is required"),
  city: yup.string().required("City is required"),
  streetAddress: yup
    .string()
    .test("no-leading-space", "Street address cannot start with a space", (v) => !/^\s/.test(v || "")),
  zipCode: yup
    .string()
    .test("no-leading-space", "ZIP/Postal cannot start with a space", (v) => !/^\s/.test(v || "")),
  registrationNumber: yup
    .string()
    .required("Tax ID/ Business Registration Number is required")
    .test("no-leading-space", "Registration Number cannot start with a space", (v) => !/^\s/.test(v || "")),
});

export interface CompanyDetailsFormData {
  companyName: string;
  companyType: string;
  numberOfStores: number;
  websiteURL: string;
  country: string;
  state: string;
  city: string;
  streetAddress: string;
  zipCode: string;
  registrationNumber: string;
}

export default function CompanyDetailsPage() {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CompanyDetailsFormData>({
    resolver: yupResolver(schema),
    mode: "onChange",           // Validates on each input change
    reValidateMode: "onChange",
  });

  const tenantId = getUserTenantId();

  // Dropdown dependencies
  const selectedCountryCode = watch("country");
  const selectedStateCode = watch("state");

  // API States
  const [isLoading, setIsLoading] = useState(false);
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const location = useLocation();
  const { toasts, removeToast, success, error } = useToast();

  // Data hooks - Always enable both API calls with forced refetch
  const { data, refetch, isLoading: isCompanyDetailsLoading } = useGetCompanyDetails(tenantId, true);
  const CompanyDetails = data?.result?.data || {};
  const updateMutation = useUpdateCompanyDetails();
  const { data: companyTypes, isLoading: isCompanyTypesLoading, refetch: refetchCompanyTypes } = useGetCompanyTypes(true);

  // Load all countries at mount and force re-initialization
  useEffect(() => {
    setCountries(Country.getAllCountries());
    setIsInitialized(false); // Reset initialization flag
  }, []);

  // Detect route changes and force re-initialization
  useEffect(() => {
    // Only trigger when navigating TO this specific route
    if (location.pathname === '/company/details') {
      // Reset initialization when location changes (navigating back to this page)
      setIsInitialized(false);
      setStates([]);
      setCities([]);
      // Reset form to ensure clean state
      reset();
      // Force refetch of both APIs
      refetch();
      refetchCompanyTypes();
    }
  }, [location.pathname, reset, refetch, refetchCompanyTypes]);

  // Force re-initialization when returning to the page or when data changes
  useEffect(() => {
    if (CompanyDetails && Object.keys(CompanyDetails).length > 0 && !isInitialized) {
      // Clear existing dropdown states to force re-population
      setStates([]);
      setCities([]);
      setIsInitialized(true);
    }
  }, [CompanyDetails, isInitialized]);

  // Load states when country changes
  useEffect(() => {
    if (selectedCountryCode) {
      const statesData = State.getStatesOfCountry(selectedCountryCode);
      setStates(statesData);
      setValue("state", "");
      setValue("city", "");
      setCities([]);
    } else {
      setStates([]);
      setCities([]);
    }
    // eslint-disable-next-line
  }, [selectedCountryCode]);

  // Load cities when state changes
  useEffect(() => {
    if (selectedCountryCode && selectedStateCode) {
      const citiesData = City.getCitiesOfState(selectedCountryCode, selectedStateCode);
      setCities(citiesData);
      setValue("city", "");
    } else {
      setCities([]);
    }
    // eslint-disable-next-line
  }, [selectedStateCode, selectedCountryCode]);

  // ------------- OPTIMIZED DEFAULT VALUE SETTING FLOW -------------

  // 1. Set basic fields + companyType & country (if ready)
  useEffect(() => {
    if (!CompanyDetails || Object.keys(CompanyDetails).length === 0 || !isInitialized) return;

    // Set basic text fields
    setValue("companyName", CompanyDetails.tenant_name || "");
    setValue("numberOfStores", CompanyDetails.number_of_locations?.toString() || "");
    setValue("websiteURL", CompanyDetails.website_url || "");
    setValue("streetAddress", CompanyDetails.street_address || "");
    setValue("zipCode", CompanyDetails.postal_code || "");
    setValue("registrationNumber", CompanyDetails.tax_id || "");

    // Set country right away (triggers state effect)
    if (CompanyDetails.country) {
      setValue("country", CompanyDetails.country);
    }

    // Set companyType when companyTypes are loaded
    if (CompanyDetails.company_type_id && companyTypes?.length) {
      const companyTypeId = CompanyDetails.company_type_id.toString();
      const validCompanyType = companyTypes.some(
        (t) => t.industry_id.toString() === companyTypeId
      );
      if (validCompanyType) {
        setValue("companyType", companyTypeId);
      }
    }
  }, [CompanyDetails, companyTypes, setValue, isInitialized]);

  // 2. Set state only when 'states' are loaded and we have a valid value
  useEffect(() => {
    if (!CompanyDetails?.state || !states.length || !isInitialized) return;

    const validState = states.some((s) => s.isoCode === CompanyDetails.state);
    if (validState) {
      setValue("state", CompanyDetails.state);
    }
  }, [states, CompanyDetails?.state, setValue, isInitialized]);

  // 3. Set city only when 'cities' are loaded and we have a valid value
  useEffect(() => {
    if (!CompanyDetails?.city || !cities.length || !isInitialized) return;

    const validCity = cities.some((c) => c.name === CompanyDetails.city);
    if (validCity) {
      setValue("city", CompanyDetails.city);
    }
  }, [cities, CompanyDetails?.city, setValue, isInitialized]);

  // If cancel, revert edit mode & re-hydrate form
  const handleCancel = () => {
    setIsEditMode(false);

    // Force re-initialization to re-populate all form fields
    setIsInitialized(false);

    // Reset form first
    reset();

    // Clear dropdown states to force re-population
    setStates([]);
    setCities([]);

    // Trigger re-initialization
    setTimeout(() => {
      setIsInitialized(true);
    }, 100);
  };

  // ----- SUBMIT -----
  const onSubmit = async (formData: CompanyDetailsFormData) => {
    if (!isEditMode) return;
    setIsLoading(true);
    try {
      await updateMutation.mutateAsync({
        tenantId,
        data: {
          tenant_name: formData.companyName,
          company_type: formData.companyType,
          number_of_locations: Number(formData.numberOfStores),
          website_url: formData.websiteURL,
          country: formData.country,
          state: formData.state,
          city: formData.city,
          street_address: formData.streetAddress,
          postal_code: formData.zipCode,
          tax_id: formData.registrationNumber,
        },
      });
      success("Success", `Company Details Updated successfully.`);
      setIsEditMode(false);
      await refetch();
    } catch (err) {
      console.log("company detail error", err);
      error("Error", `Something went wrong updating details.`);
    } finally {
      setIsLoading(false);
    }
  };

  // ---- RENDER ----
  return (
    <>
      <div className="grid gap-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-4 h-[67px]">
            <CardTitle>Company Details</CardTitle>
            {!isEditMode && (
              <Button
                type="button"
                className="gap-2 px-3 h-[34px]"
                onClick={() => setIsEditMode(true)}
              >
                <Pencil size={14} />
                Edit
              </Button>
            )}
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {/* Company Name */}
                <div>
                  <Label htmlFor="companyName">
                    Company Name<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="companyName"
                    type="text"
                    {...register("companyName")}
                    disabled={!isEditMode}
                    className={errors.companyName ? "border-destructive" : ""}
                    placeholder="Enter company name"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.companyName && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.companyName.message}
                    </p>
                  )}
                </div>
                {/* Company Type */}
                <div>
                  <Label htmlFor="companyType">
                    Company Type<span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch("companyType")}
                    onValueChange={(val) => setValue("companyType", val)}
                    disabled={!isEditMode}
                  >
                    <SelectTrigger
                      id="companyType"
                      className={errors.companyType ? "border-destructive" : ""}
                    >
                      <SelectValue
                        placeholder={
                          isCompanyTypesLoading ? "Loading..." : "Select type"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {isCompanyTypesLoading ? (
                        <SelectItem value="loading" disabled>
                          Loading...
                        </SelectItem>
                      ) : (
                        companyTypes?.map((type) => (
                          <SelectItem
                            key={type.industry_id}
                            value={type.industry_id.toString()}
                          >
                            {type.industry_name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.companyType && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.companyType.message}
                    </p>
                  )}
                </div>
                {/* Number of Stores */}
                <div>
                  <Label htmlFor="numberOfStores">Number of Stores</Label>
                  <Input
                    id="numberOfStores"
                    type="number"
                    {...register("numberOfStores")}
                    disabled={!isEditMode}
                    className={errors.numberOfStores ? "border-destructive" : ""}
                    placeholder="Enter number of stores"
                  />
                  {errors.numberOfStores && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.numberOfStores.message}
                    </p>
                  )}
                </div>
                {/* Website URL */}
                <div>
                  <Label htmlFor="websiteURL">Website URL</Label>
                  <Input
                    id="websiteURL"
                    type="text"
                    {...register("websiteURL")}
                    disabled={!isEditMode}
                    className={errors.websiteURL ? "border-destructive" : ""}
                    placeholder="Enter website URL"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.websiteURL && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.websiteURL.message}
                    </p>
                  )}
                </div>
                {/* Country */}
                <div>
                  <Label htmlFor="country">
                    Country<span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch("country")}
                    onValueChange={(val) => setValue("country", val)}
                    disabled={!isEditMode}
                  >
                    <SelectTrigger
                      id="country"
                      className={errors.country ? "border-destructive" : ""}
                    >
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem
                          key={country.isoCode}
                          value={country.isoCode}
                        >
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.country && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.country.message}
                    </p>
                  )}
                </div>
                {/* State / Region */}
                <div>
                  <Label htmlFor="state">
                    State / Region<span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch("state")}
                    onValueChange={(val) => setValue("state", val)}
                    disabled={!states.length || !isEditMode}
                  >
                    <SelectTrigger
                      id="state"
                      className={errors.state ? "border-destructive" : ""}
                      disabled={!states.length || !isEditMode}
                    >
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>
                      {states.map((state) => (
                        <SelectItem key={state.isoCode} value={state.isoCode}>
                          {state.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.state && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.state.message}
                    </p>
                  )}
                </div>
                {/* City */}
                <div>
                  <Label htmlFor="city">
                    City<span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch("city")}
                    onValueChange={(val) => setValue("city", val)}
                    disabled={!cities.length || !isEditMode}
                  >
                    <SelectTrigger
                      id="city"
                      className={errors.city ? "border-destructive" : ""}
                      disabled={!cities.length || !isEditMode}
                    >
                      <SelectValue placeholder="Select city" />
                    </SelectTrigger>
                    <SelectContent>
                      {cities.map((city) => (
                        <SelectItem key={city.name} value={city.name}>
                          {city.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.city && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.city.message}
                    </p>
                  )}
                </div>
                {/* Street Address */}
                <div>
                  <Label htmlFor="streetAddress">Street Address</Label>
                  <Input
                    id="streetAddress"
                    type="text"
                    {...register("streetAddress")}
                    disabled={!isEditMode}
                    className={errors.streetAddress ? "border-destructive" : ""}
                    placeholder="Enter street address"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.streetAddress && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.streetAddress.message}
                    </p>
                  )}
                </div>
                {/* ZIP/Postal Code */}
                <div>
                  <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                  <Input
                    id="zipCode"
                    type="text"
                    {...register("zipCode")}
                    disabled={!isEditMode}
                    className={errors.zipCode ? "border-destructive" : ""}
                    placeholder="Enter ZIP/Postal Code"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.zipCode && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.zipCode.message}
                    </p>
                  )}
                </div>
                {/* Tax ID/Business Registration Number */}
                <div>
                  <Label htmlFor="registrationNumber">
                    Tax ID/ Business Registration Number
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="registrationNumber"
                    type="text"
                    {...register("registrationNumber")}
                    disabled={!isEditMode}
                    className={errors.registrationNumber ? "border-destructive" : ""}
                    placeholder="Enter registration number"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.registrationNumber && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.registrationNumber.message}
                    </p>
                  )}
                </div>
              </div>
              {/* Save/Update Button */}
              {isEditMode && (
                <div className="mt-8 pt-4 border-t flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="gap-2"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="gap-2"
                  >
                    <Save size={16} />
                    {isLoading ? "Updating..." : "Update"}
                  </Button>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </div>
    </>
  );
}
